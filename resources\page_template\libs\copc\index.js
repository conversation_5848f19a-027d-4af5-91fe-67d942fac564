var Copc;(()=>{var e={629:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hierarchyItemLength=t.infoLength=void 0,t.infoLength=160,t.hierarchyItemLength=32},156:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Copc=void 0;const a=o(r(693)),s=r(115),u=r(277),c=r(216);async function l(e,{pointDataRecordFormat:t,pointDataRecordLength:r},n,i){const o=await async function(e,{pointDataOffset:t,pointDataLength:r}){return s.Getter.create(e)(t,t+r)}(e,n),{pointCount:u}=n;return a.PointData.decompressChunk(o,{pointCount:u,pointDataRecordFormat:t,pointDataRecordLength:r},i)}t.Copc={create:async function(e){const t=s.Getter.create(e),r=t(0,65536);async function n(e,n){return n>=65536?t(e,n):(await r).slice(e,n)}const i=a.Header.parse(await n(0,a.Constants.minHeaderLength)),o=await a.Vlr.walk(n,i),u=a.Vlr.find(o,"copc",1);if(!u)throw new Error("COPC info VLR is required");const l=c.Info.parse(await a.Vlr.fetch(n,u));let d;const f=a.Vlr.find(o,"LASF_Projection",2112);f&&f.contentLength&&(d=s.Binary.toCString(await a.Vlr.fetch(n,f)),""===d&&(d=void 0));let p=[];const h=a.Vlr.find(o,"LASF_Spec",4);return h&&(p=a.ExtraBytes.parse(await a.Vlr.fetch(n,h))),{header:i,vlrs:o,info:l,wkt:d,eb:p}},loadHierarchyPage:async function(e,t){const r=s.Getter.create(e);return u.Hierarchy.load(r,t)},loadPointDataBuffer:l,loadPointDataView:async function(e,t,r,{lazPerf:n,include:i}={}){const o=await l(e,t.header,r,n);return a.View.create(o,t.header,t.eb,i)}}},277:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Hierarchy=void 0;const n=r(115),i=r(629);function o(e){const t=n.Binary.toDataView(e);if(t.byteLength%i.hierarchyItemLength!=0)throw new Error(`Invalid hierarchy page length: ${t.byteLength}`);const r={},o={};for(let e=0;e<t.byteLength;e+=i.hierarchyItemLength){const i=t.getInt32(e+0,!0),a=t.getInt32(e+4,!0),s=t.getInt32(e+8,!0),u=t.getInt32(e+12,!0),c=(0,n.parseBigInt)((0,n.getBigUint64)(t,e+16,!0)),l=t.getInt32(e+24,!0),d=t.getInt32(e+28,!0),f=n.Key.toString([i,a,s,u]);if(d<-1)throw new Error(`Invalid hierarchy point count at key: ${f}`);-1===d?o[f]={pageOffset:c,pageLength:l}:r[f]={pointCount:d,pointDataOffset:c,pointDataLength:l}}return{nodes:r,pages:o}}t.Hierarchy={parse:o,load:async function(e,t){const r=n.Getter.create(e);return o(await r(t.pageOffset,t.pageOffset+t.pageLength))}}},750:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Info=t.Hierarchy=t.Copc=t.Constants=void 0,t.Constants=o(r(629));var a=r(156);Object.defineProperty(t,"Copc",{enumerable:!0,get:function(){return a.Copc}});var s=r(277);Object.defineProperty(t,"Hierarchy",{enumerable:!0,get:function(){return s.Hierarchy}});var u=r(216);Object.defineProperty(t,"Info",{enumerable:!0,get:function(){return u.Info}})},216:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Info=void 0;const n=r(115),i=r(629);t.Info={parse:function(e){const t=n.Binary.toDataView(e);if(t.byteLength!==i.infoLength)throw new Error(`Invalid COPC info VLR length (should be ${i.infoLength}): ${t.byteLength}`);const r=[t.getFloat64(0,!0),t.getFloat64(8,!0),t.getFloat64(16,!0)],o=t.getFloat64(24,!0);return{cube:[r[0]-o,r[1]-o,r[2]-o,r[0]+o,r[1]+o,r[2]+o],spacing:t.getFloat64(32,!0),rootHierarchyPage:{pageOffset:(0,n.parseBigInt)((0,n.getBigUint64)(t,40,!0)),pageLength:(0,n.parseBigInt)((0,n.getBigUint64)(t,48,!0))},gpsTimeRange:[t.getFloat64(56,!0),t.getFloat64(64,!0)]}}}},975:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},212:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Hierarchy=void 0,t.Hierarchy={parse:function(e){return Object.entries(e).reduce(((e,[t,r])=>(-1===r?e.pages[t]={}:r&&(e.nodes[t]={pointCount:r}),e)),{nodes:{},pages:{}})}}},329:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(975),t),i(r(212),t)},830:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t},a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.Las=t.Ept=void 0,t.Ept=o(r(329)),a(r(750),t),t.Las=o(r(693)),a(r(115),t)},755:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.evlrHeaderLength=t.vlrHeaderLength=t.minHeaderLength=void 0,t.minHeaderLength=375,t.vlrHeaderLength=54,t.evlrHeaderLength=60},953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Dimensions=void 0;const n=r(115),i=r(125);t.Dimensions={create:function(e,t=[]){return Object.keys(e).reduce(((e,r)=>{const n=a[r];if(n)return{...e,[r]:n};const o=t.find((e=>e.name===r)),s=o&&i.ExtraBytes.getDimension(o);if(s)return{...e,[r]:s};throw new Error(`Failed to look up LAS type: ${r}`)}),{})}};const{Type:o}=n.Dimension,a={X:o.float64,Y:o.float64,Z:o.float64,Intensity:o.uint16,ReturnNumber:o.uint8,NumberOfReturns:o.uint8,ScanDirectionFlag:o.boolean,EdgeOfFlightLine:o.boolean,Classification:o.uint8,Synthetic:o.boolean,KeyPoint:o.boolean,Withheld:o.boolean,Overlap:o.boolean,ScanAngle:o.float32,UserData:o.uint8,PointSourceId:o.uint16,GpsTime:o.float64,Red:o.uint16,Green:o.uint16,Blue:o.uint16,ScannerChannel:o.uint8,Infrared:o.uint16}},125:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ExtraBytes=void 0;const n=r(115);t.ExtraBytes={getDimension:function({type:e,length:t}){switch(e){case"signed":case"unsigned":switch(t){case 1:case 2:case 4:case 8:return{type:e,size:t}}case"float":switch(t){case 4:case 8:return{type:e,size:t}}}},parse:function(e){if(e.byteLength%i!=0)throw new Error(`Invalid extra bytes VLR length: ${e.byteLength}`);const t=[];for(let r=0;r<e.byteLength;r+=i)t.push(o(e.slice(r,r+i)));return t},parseOne:o};const i=192;function o(e){if(e.byteLength!==i)throw new Error(`Invalid extra bytes entry length: ${e.byteLength}`);const t=n.Binary.toDataView(e),r=n.Binary.toCString(e.slice(4,36)),o=n.Binary.toCString(e.slice(60,192)),a=t.getUint8(2),s=t.getUint8(3);if(a>=11)throw new Error(`Invalid extra bytes "type" value: ${a}`);if(0===a)return{name:r,description:o,length:s};const u=(c=s,{hasNodata:Boolean(1&c),hasMin:Boolean(c>>1&1),hasMax:Boolean(c>>2&1),hasScale:Boolean(c>>3&1),hasOffset:Boolean(c>>4&1)});var c;const l=function(e){switch(e){case 1:return n.Dimension.Type.uint8;case 2:return n.Dimension.Type.int8;case 3:return n.Dimension.Type.uint16;case 4:return n.Dimension.Type.int16;case 5:return n.Dimension.Type.uint32;case 6:return n.Dimension.Type.int32;case 7:return n.Dimension.Type.uint64;case 8:return n.Dimension.Type.int64;case 9:return n.Dimension.Type.float32;case 10:return n.Dimension.Type.float64}}(a);if(!l)throw new Error(`Failed to extract dimension type: ${a}`);const{type:d,size:f}=l;function p(e){switch(d){case"signed":return(0,n.parseBigInt)(t.getBigInt64(e,!0));case"unsigned":return(0,n.parseBigInt)((0,n.getBigUint64)(t,e,!0));case"float":return t.getFloat64(e,!0)}}const h={name:r,description:o,type:d,length:f};return u.hasNodata&&(h.nodata=p(40)),u.hasMin&&(h.min=p(64)),u.hasMax&&(h.max=p(88)),u.hasScale&&(h.scale=t.getFloat64(112)),u.hasOffset&&(h.offset=t.getFloat64(136)),h}},32:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Extractor=void 0;const n=r(115);function i(e){const{scale:t,offset:r}=e,i=s(e);function o(e,t){return e.getUint8(i(t)+14)}function a(e,t){return e.getUint8(i(t)+15)}function u(e,t){return 31&a(e,t)}return{X:(e,o)=>n.Scale.unapply(e.getInt32(i(o),!0),t[0],r[0]),Y:(e,o)=>n.Scale.unapply(e.getInt32(i(o)+4,!0),t[1],r[1]),Z:(e,o)=>n.Scale.unapply(e.getInt32(i(o)+8,!0),t[2],r[2]),Intensity:(e,t)=>e.getUint16(i(t)+12,!0),ReturnNumber:(e,t)=>7&o(e,t),NumberOfReturns:(e,t)=>(56&o(e,t))>>3,ScanDirectionFlag:(e,t)=>(64&o(e,t))>>6,EdgeOfFlightLine:(e,t)=>(128&o(e,t))>>7,Classification:(e,t)=>{const r=u(e,t);return 12===r?0:r},Synthetic:(e,t)=>(32&a(e,t))>>5,KeyPoint:(e,t)=>(64&a(e,t))>>6,Withheld:(e,t)=>(128&a(e,t))>>7,Overlap:(e,t)=>12===u(e,t)?1:0,ScanAngle:(e,t)=>e.getInt8(i(t)+16),UserData:(e,t)=>e.getUint8(i(t)+17),PointSourceId:(e,t)=>e.getUint16(i(t)+18,!0)}}function o(e){const{scale:t,offset:r}=e,i=s(e);function o(e,t){return e.getUint8(i(t)+15)}return{X:(e,o)=>n.Scale.unapply(e.getInt32(i(o),!0),t[0],r[0]),Y:(e,o)=>n.Scale.unapply(e.getInt32(i(o)+4,!0),t[1],r[1]),Z:(e,o)=>n.Scale.unapply(e.getInt32(i(o)+8,!0),t[2],r[2]),Intensity:(e,t)=>e.getUint16(i(t)+12,!0),ReturnNumber:(e,t)=>15&e.getUint16(i(t)+14,!0),NumberOfReturns:(e,t)=>(240&e.getUint16(i(t)+14,!0))>>4,Synthetic:(e,t)=>1&o(e,t),KeyPoint:(e,t)=>(2&o(e,t))>>1,Withheld:(e,t)=>(4&o(e,t))>>2,Overlap:(e,t)=>(8&o(e,t))>>3,ScannerChannel:(e,t)=>(48&o(e,t))>>4,ScanDirectionFlag:(e,t)=>(64&o(e,t))>>6,EdgeOfFlightLine:(e,t)=>(128&o(e,t))>>7,Classification:(e,t)=>e.getUint8(i(t)+16),UserData:(e,t)=>e.getUint8(i(t)+17),ScanAngle:(e,t)=>.006*e.getInt16(i(t)+18,!0),PointSourceId:(e,t)=>e.getUint16(i(t)+20,!0),GpsTime:(e,t)=>e.getFloat64(i(t)+22,!0)}}function a(e){const t=s(e);return{...o(e),Red:(e,r)=>e.getUint16(t(r)+30,!0),Green:(e,r)=>e.getUint16(t(r)+32,!0),Blue:(e,r)=>e.getUint16(t(r)+34,!0)}}function s(e){const{pointDataRecordLength:t}=e;return function(e){return e*t}}t.Extractor={create:function(e,t=[]){const r=function(e,t){let r=function(e){switch(e){case 0:return 20;case 1:return 28;case 2:return 26;case 3:return 34;case 6:return 30;case 7:return 36;case 8:return 38;default:throw new Error(`Unsupported point data record format: ${e}`)}}(e.pointDataRecordFormat);return t.reduce(((t,i)=>{const o=r;r+=i.length;const a=function(e,t,{type:r,length:i}){const o=s(e);switch(r){case"signed":switch(i){case 1:return(e,r)=>e.getInt8(o(r)+t);case 2:return(e,r)=>e.getInt16(o(r)+t,!0);case 4:return(e,r)=>e.getInt32(o(r)+t,!0);case 8:return(e,r)=>(0,n.parseBigInt)(e.getBigInt64(o(r)+t,!0))}case"unsigned":switch(i){case 1:return(e,r)=>e.getUint8(o(r)+t);case 2:return(e,r)=>e.getUint16(o(r)+t,!0);case 4:return(e,r)=>e.getUint32(o(r)+t,!0);case 8:return(e,r)=>(0,n.parseBigInt)((0,n.getBigUint64)(e,o(r)+t,!0))}case"float":switch(i){case 4:return(e,r)=>e.getFloat32(o(r)+t,!0);case 8:return(e,r)=>e.getFloat64(o(r)+t,!0)}}}(e,o,i);if(!a)return t;return{...t,[i.name]:(e,t)=>n.Scale.unapply(a(e,t),i.scale,i.offset)}}),{})}(e,t);return{...(()=>{const{pointDataRecordFormat:t}=e;switch(t){case 0:return i(e);case 1:return function(e){const t=s(e);return{...i(e),GpsTime:(e,r)=>e.getFloat64(t(r)+20,!0)}}(e);case 2:return function(e){const t=s(e);return{...i(e),Red:(e,r)=>e.getUint16(t(r)+20,!0),Green:(e,r)=>e.getUint16(t(r)+22,!0),Blue:(e,r)=>e.getUint16(t(r)+24,!0)}}(e);case 3:return function(e){const t=s(e);return{...i(e),GpsTime:(e,r)=>e.getFloat64(t(r)+20,!0),Red:(e,r)=>e.getUint16(t(r)+28,!0),Green:(e,r)=>e.getUint16(t(r)+30,!0),Blue:(e,r)=>e.getUint16(t(r)+32,!0)}}(e);case 6:return o(e);case 7:return a(e);case 8:return function(e){const t=s(e);return{...a(e),Infrared:(e,r)=>e.getUint16(t(r)+36,!0)}}(e);default:throw new Error(`Unsupported point data record format: ${t}`)}})(),...r}}}},651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Header=void 0;const n=r(115),i=r(755),o=r(267);function a(e){const t=n.Binary.toDataView(e),r=[];for(let e=0;e<120;e+=8)r.push((0,n.getBigUint64)(t,e,!0));return r.map((e=>(0,n.parseBigInt)(e)))}function s(e){const t=n.Binary.toDataView(e),r=[];for(let e=0;e<20;e+=4)r.push(t.getUint32(e,!0));return r}t.Header={parse:function(e){if(e.byteLength<i.minHeaderLength)throw new Error(`Invalid header: must be at least ${i.minHeaderLength} bytes`);const t=n.Binary.toDataView(e),r=n.Binary.toCString(e.slice(0,4));if("LASF"!==r)throw new Error(`Invalid file signature: ${r}`);const u=t.getUint8(24),c=t.getUint8(25);if(1!==u||2!==c&&4!==c)throw new Error(`Invalid version (only 1.2 and 1.4 supported): ${u}.${c}`);const l={fileSignature:r,fileSourceId:t.getUint16(4,!0),globalEncoding:t.getUint16(6,!0),projectId:(0,o.formatGuid)(e.slice(8,24)),majorVersion:u,minorVersion:c,systemIdentifier:n.Binary.toCString(e.slice(26,58)),generatingSoftware:n.Binary.toCString(e.slice(58,90)),fileCreationDayOfYear:t.getUint16(90,!0),fileCreationYear:t.getUint16(92,!0),headerLength:t.getUint16(94,!0),pointDataOffset:t.getUint32(96,!0),vlrCount:t.getUint32(100,!0),pointDataRecordFormat:15&t.getUint8(104),pointDataRecordLength:t.getUint16(105,!0),pointCount:t.getUint32(107,!0),pointCountByReturn:s(e.slice(111,131)),scale:(0,o.parsePoint)(e.slice(131,155)),offset:(0,o.parsePoint)(e.slice(155,179)),min:[t.getFloat64(187,!0),t.getFloat64(203,!0),t.getFloat64(219,!0)],max:[t.getFloat64(179,!0),t.getFloat64(195,!0),t.getFloat64(211,!0)],waveformDataOffset:0,evlrOffset:0,evlrCount:0};return 2==c?l:{...l,pointCount:(0,n.parseBigInt)((0,n.getBigUint64)(t,247,!0)),pointCountByReturn:a(e.slice(255,375)),waveformDataOffset:(0,n.parseBigInt)((0,n.getBigUint64)(t,227,!0)),evlrOffset:(0,n.parseBigInt)((0,n.getBigUint64)(t,235,!0)),evlrCount:t.getUint32(243,!0)}}}},693:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Vlr=t.View=t.PointData=t.Header=t.Extractor=t.ExtraBytes=t.Dimensions=t.Constants=void 0,t.Constants=o(r(755));var a=r(953);Object.defineProperty(t,"Dimensions",{enumerable:!0,get:function(){return a.Dimensions}});var s=r(125);Object.defineProperty(t,"ExtraBytes",{enumerable:!0,get:function(){return s.ExtraBytes}});var u=r(32);Object.defineProperty(t,"Extractor",{enumerable:!0,get:function(){return u.Extractor}});var c=r(651);Object.defineProperty(t,"Header",{enumerable:!0,get:function(){return c.Header}});var l=r(141);Object.defineProperty(t,"PointData",{enumerable:!0,get:function(){return l.PointData}});var d=r(92);Object.defineProperty(t,"View",{enumerable:!0,get:function(){return d.View}});var f=r(687);Object.defineProperty(t,"Vlr",{enumerable:!0,get:function(){return f.Vlr}})},141:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decompressFile=t.decompressChunk=t.PointData=void 0;const n=r(622),i=r(651);let o;async function a(e){return e||(o||(o=(0,n.createLazPerf)()),o)}async function s(e,{pointCount:t,pointDataRecordFormat:r,pointDataRecordLength:n},i){const o=await a(i),s=new Uint8Array(t*n),u=o._malloc(e.byteLength),c=o._malloc(n),l=new o.ChunkDecoder;try{o.HEAPU8.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),u),l.open(r,n,u);for(let e=0;e<t;++e)l.getPoint(c),s.set(new Uint8Array(o.HEAPU8.buffer,c,n),e*n)}finally{o._free(u),o._free(c),l.delete()}return s}async function u(e,t){const r=await a(t),n=i.Header.parse(e),{pointCount:o,pointDataRecordLength:s}=n,u=new Uint8Array(o*s),c=r._malloc(e.byteLength),l=r._malloc(s),d=new r.LASZip;try{r.HEAPU8.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),c),d.open(c,e.byteLength);for(let e=0;e<o;++e)d.getPoint(l),u.set(new Uint8Array(r.HEAPU8.buffer,l,s),e*s)}finally{d.delete()}return u}t.PointData={createLazPerf:n.createLazPerf,decompressChunk:s,decompressFile:u},t.decompressChunk=s,t.decompressFile=u},267:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatGuid=t.parsePoint=void 0;const n=r(115);t.parsePoint=function(e){const t=n.Binary.toDataView(e);if(24!==t.byteLength)throw new Error(`Invalid tuple buffer length: ${t.byteLength}`);return[t.getFloat64(0,!0),t.getFloat64(8,!0),t.getFloat64(16,!0)]},t.formatGuid=function(e){const t=n.Binary.toDataView(e);if(16!==t.byteLength)throw new Error(`Invalid GUID buffer length: ${t.byteLength}`);let r="";for(let e=0;e<t.byteLength;e+=4)r+=t.getUint32(e,!0).toString(16).padStart(8,"0");return[r.slice(0,8),r.slice(8,12),r.slice(12,16),r.slice(16,32)].join("-")}},92:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.View=void 0;const n=r(115),i=r(953),o=r(32);t.View={create:function(e,t,r=[],a){let s=o.Extractor.create(t,r);if(a){const e=new Set([...a]);s=Object.entries(s).reduce(((t,[r,n])=>(e.has(r)&&(t[r]=n),t)),{})}const u=i.Dimensions.create(s,r),c=n.Binary.toDataView(e),l=t.pointDataRecordLength;if(c.byteLength%l!=0)throw new Error(`Invalid buffer length (${c.byteLength}) for point length ${l}`);const d=c.byteLength/t.pointDataRecordLength;return{pointCount:d,dimensions:u,getter:function(e){const t=s[e];if(!t)throw new Error(`No extractor for dimension: ${e}`);return function(e){if(e>=d)throw new RangeError(`View index (${e}) out of range: ${d}`);return t(c,e)}}}}}},687:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Vlr=void 0;const n=r(115),i=r(755);function o(e,t,r){return e.find((e=>e.userId===t&&e.recordId===r))}function a(e,t){return(t?u:s)(e)}function s(e){const t=n.Binary.toDataView(e);if(t.byteLength!==i.vlrHeaderLength)throw new Error(`Invalid VLR header length (must be ${i.vlrHeaderLength}): ${t.byteLength}`);return{userId:n.Binary.toCString(e.slice(2,18)),recordId:t.getUint16(18,!0),contentLength:t.getUint16(20,!0),description:n.Binary.toCString(e.slice(22,54)),isExtended:!1}}function u(e){const t=n.Binary.toDataView(e);if(t.byteLength!==i.evlrHeaderLength)throw new Error(`Invalid EVLR header length (must be ${i.evlrHeaderLength}): ${t.byteLength}`);return{userId:n.Binary.toCString(e.slice(2,18)),recordId:t.getUint16(18,!0),contentLength:(0,n.parseBigInt)((0,n.getBigUint64)(t,20,!0)),description:n.Binary.toCString(e.slice(28,60)),isExtended:!0}}async function c({get:e,startOffset:t,count:r,isExtended:n}){const o=[];let s=t;const u=n?i.evlrHeaderLength:i.vlrHeaderLength;for(let t=0;t<r;++t){const t=u?await e(s,s+u):new Uint8Array,{userId:r,recordId:i,contentLength:c,description:l}=a(t,n);o.push({userId:r,recordId:i,contentOffset:s+u,contentLength:c,description:l,isExtended:n}),s+=u+c}return o}t.Vlr={walk:async function(e,t){const r=n.Getter.create(e);return[...await c({get:r,startOffset:t.headerLength,count:t.vlrCount,isExtended:!1}),...await c({get:r,startOffset:t.evlrOffset,count:t.evlrCount,isExtended:!0})]},parse:a,find:o,at:function(e,t,r){const n=o(e,t,r);if(!n)throw new Error(`VLR not found: ${t}/${r}`);return n},fetch:function(e,{contentOffset:t,contentLength:r}){return 0===r?new Uint8Array:n.Getter.create(e)(t,t+r)}}},877:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBigUint64=t.parseBigInt=void 0,t.parseBigInt=function(e){if(e>BigInt(Number.MAX_SAFE_INTEGER)||e<BigInt(-Number.MAX_SAFE_INTEGER))throw new Error(`Cannot convert bigint to number: ${e}`);return Number(e)},t.getBigUint64=function(e,t,r){if(e.getBigUint64)return e.getBigUint64(t,r);const[n,i]=r?[4,0]:[0,4],o=BigInt(e.getUint32(t+n,r)),a=BigInt(e.getUint32(t+i,r));return(o<<BigInt(32))+a}},467:(e,t)=>{"use strict";function r(e){return new DataView(e.buffer,e.byteOffset,e.length)}function n(e){const t=r(e);let n="";for(let e=0;e<t.byteLength;++e){const r=t.getInt8(e);if(0===r)return n;n+=String.fromCharCode(r)}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.toCString=t.toDataView=t.Binary=void 0,t.Binary={toDataView:r,toCString:n},t.toDataView=r,t.toCString=n},314:(e,t)=>{"use strict";function r([e,t,r,n,i,o]){return[e+(n-e)/2,t+(i-t)/2,r+(o-r)/2]}function n(e){return e[3]-e[0]}function i(e){return e[4]-e[1]}function o(e){return e[5]-e[2]}function a(e,[t,n,i]){const[o,a,s,u,c,l]=e,[d,f,p]=r(e);return[t?d:o,n?f:a,i?p:s,t?u:d,n?c:f,i?l:p]}Object.defineProperty(t,"__esModule",{value:!0}),t.Bounds=void 0,t.Bounds={min:function(e){return[e[0],e[1],e[2]]},max:function(e){return[e[3],e[4],e[5]]},mid:r,width:n,depth:i,height:o,cube:function(e){const t=r(e),a=Math.max(n(e),i(e),o(e))/2;return[t[0]-a,t[1]-a,t[2]-a,t[0]+a,t[1]+a,t[2]+a]},step:a,stepTo:function(e,[t,r,n,i]){for(let o=t-1;o>=0;--o)e=a(e,[r>>o&1,n>>o&1,i>>o&1]);return e},intersection:function(e,t){return[Math.max(e[0],t[0]),Math.max(e[1],t[1]),Math.max(e[2],t[2]),Math.min(e[3],t[3]),Math.min(e[4],t[4]),Math.min(e[5],t[5])]}}},882:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Dimension=void 0,t.Dimension={Type:{int8:{type:"signed",size:1},int16:{type:"signed",size:2},int32:{type:"signed",size:4},int64:{type:"signed",size:8},uint8:{type:"unsigned",size:1},uint16:{type:"unsigned",size:2},uint32:{type:"unsigned",size:4},uint64:{type:"unsigned",size:8},float32:{type:"float",size:4},float64:{type:"float",size:8},float:{type:"float",size:4},double:{type:"float",size:8},bool:{type:"unsigned",size:1},boolean:{type:"unsigned",size:1}},ctype:function({type:e,size:t}){switch(e){case"signed":switch(t){case 1:return"int8";case 2:return"int16";case 4:return"int32";case 8:return"int64"}case"unsigned":switch(t){case 1:return"uint8";case 2:return"uint16";case 4:return"uint32";case 8:return"uint64"}case"float":switch(t){case 4:return"float";case 8:return"double"}}throw new Error(`Invalid dimension type/size: ${e}/${t}`)}}},732:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Getter=void 0;const s=a(r(98));function u(e){return async function(t,r){if(t<0||r<0||t>r)throw new Error("Invalid range");const n=await(0,s.default)(e,{headers:{Range:`bytes=${t}-${r-1}`}}),i=await n.arrayBuffer();return new Uint8Array(i)}}function c(e){return async function(t,n){const i=await Promise.resolve().then((()=>o(r(963))));return async function(t=0,r=1/0){if(t<0||r<0||t>r)throw new Error("Invalid range");return await i.promises.access(e),async function(e){return await new Promise(((t,r)=>{const n=[];e.on("data",(e=>n.push(e))),e.on("error",r),e.on("end",(()=>t(Buffer.concat(n))))}))}(i.createReadStream(e,{start:t,end:r-1,autoClose:!0}))}(t,n)}}t.Getter={create:function(e){return"function"==typeof e?e:e.startsWith("http://")||e.startsWith("https://")?u(e):c(e)},http:u,file:c}},115:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.Step=t.Scale=t.Key=t.Getter=t.Dimension=t.Bounds=t.Binary=void 0,i(r(877),t);var o=r(467);Object.defineProperty(t,"Binary",{enumerable:!0,get:function(){return o.Binary}});var a=r(314);Object.defineProperty(t,"Bounds",{enumerable:!0,get:function(){return a.Bounds}});var s=r(882);Object.defineProperty(t,"Dimension",{enumerable:!0,get:function(){return s.Dimension}});var u=r(732);Object.defineProperty(t,"Getter",{enumerable:!0,get:function(){return u.Getter}});var c=r(479);Object.defineProperty(t,"Key",{enumerable:!0,get:function(){return c.Key}});var l=r(350);Object.defineProperty(t,"Scale",{enumerable:!0,get:function(){return l.Scale}});var d=r(265);Object.defineProperty(t,"Step",{enumerable:!0,get:function(){return d.Step}})},479:(e,t)=>{"use strict";function r(e){if("string"!=typeof e)return e;const[t,r,n,i,...o]=e.split("-").map((e=>parseInt(e,10))),a=[t,r,n,i];if(0!==o.length||a.some((e=>"number"!=typeof e||Number.isNaN(e))))throw new Error(`Invalid key: ${e}`);return a}Object.defineProperty(t,"__esModule",{value:!0}),t.Key=void 0,t.Key={create:function(e,t=0,n=0,i=0){return"number"!=typeof e?r(e):[e,t,n,i]},parse:r,toString:function(e){return"string"==typeof e?e:e.join("-")},step:function(e,[r,n,i]){const[o,a,s,u]=t.Key.create(e);return[o+1,2*a+r,2*s+n,2*u+i]},up:function(e,r=1){const[n,i,o,a]=t.Key.create(e);return[n-r,i>>r,o>>r,a>>r]},compare:function(e,t){if(e[0]<t[0])return-1;if(e[0]>t[0])return 1;for(let r=0;r<e.length;++r){if(e[r]<t[r])return-1;if(e[r]>t[r])return 1}return 0},depth:function(e){return e[0]}}},350:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Scale=void 0,t.Scale={apply:(e,t=1,r=0)=>(e-r)/t,unapply:(e,t=1,r=0)=>e*t+r}},265:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Step=void 0,t.Step={fromIndex:function(e){if(e<0||e>=8)throw new Error(`Invalid step index: ${e}`);return[e>>0&1?1:0,e>>1&1?1:0,e>>2&1?1:0]},list:function(){return[[0,0,0],[0,0,1],[0,1,0],[0,1,1],[1,0,0],[1,0,1],[1,1,0],[1,1,1]]}}},98:function(e,t){var r="undefined"!=typeof self?self:this,n=function(){function e(){this.fetch=!1,this.DOMException=r.DOMException}return e.prototype=r,new e}();!function(e){!function(t){var r="URLSearchParams"in e,n="Symbol"in e&&"iterator"in Symbol,i="FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(e){return!1}}(),o="FormData"in e,a="ArrayBuffer"in e;if(a)var s=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],u=ArrayBuffer.isView||function(e){return e&&s.indexOf(Object.prototype.toString.call(e))>-1};function c(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(e))throw new TypeError("Invalid character in header field name");return e.toLowerCase()}function l(e){return"string"!=typeof e&&(e=String(e)),e}function d(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return n&&(t[Symbol.iterator]=function(){return t}),t}function f(e){this.map={},e instanceof f?e.forEach((function(e,t){this.append(t,e)}),this):Array.isArray(e)?e.forEach((function(e){this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function p(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function h(e){return new Promise((function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}}))}function g(e){var t=new FileReader,r=h(t);return t.readAsArrayBuffer(e),r}function y(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function m(){return this.bodyUsed=!1,this._initBody=function(e){var t;this._bodyInit=e,e?"string"==typeof e?this._bodyText=e:i&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:o&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:r&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():a&&i&&(t=e)&&DataView.prototype.isPrototypeOf(t)?(this._bodyArrayBuffer=y(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(e)||u(e))?this._bodyArrayBuffer=y(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i&&(this.blob=function(){var e=p(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?p(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(g)}),this.text=function(){var e,t,r,n=p(this);if(n)return n;if(this._bodyBlob)return e=this._bodyBlob,r=h(t=new FileReader),t.readAsText(e),r;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),r=new Array(t.length),n=0;n<t.length;n++)r[n]=String.fromCharCode(t[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},o&&(this.formData=function(){return this.text().then(_)}),this.json=function(){return this.text().then(JSON.parse)},this}f.prototype.append=function(e,t){e=c(e),t=l(t);var r=this.map[e];this.map[e]=r?r+", "+t:t},f.prototype.delete=function(e){delete this.map[c(e)]},f.prototype.get=function(e){return e=c(e),this.has(e)?this.map[e]:null},f.prototype.has=function(e){return this.map.hasOwnProperty(c(e))},f.prototype.set=function(e,t){this.map[c(e)]=l(t)},f.prototype.forEach=function(e,t){for(var r in this.map)this.map.hasOwnProperty(r)&&e.call(t,this.map[r],r,this)},f.prototype.keys=function(){var e=[];return this.forEach((function(t,r){e.push(r)})),d(e)},f.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),d(e)},f.prototype.entries=function(){var e=[];return this.forEach((function(t,r){e.push([r,t])})),d(e)},n&&(f.prototype[Symbol.iterator]=f.prototype.entries);var v=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function b(e,t){var r,n,i=(t=t||{}).body;if(e instanceof b){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new f(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,i||null==e._bodyInit||(i=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",!t.headers&&this.headers||(this.headers=new f(t.headers)),this.method=(n=(r=t.method||this.method||"GET").toUpperCase(),v.indexOf(n)>-1?n:r),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(i)}function _(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var r=e.split("="),n=r.shift().replace(/\+/g," "),i=r.join("=").replace(/\+/g," ");t.append(decodeURIComponent(n),decodeURIComponent(i))}})),t}function w(e,t){t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in t?t.statusText:"OK",this.headers=new f(t.headers),this.url=t.url||"",this._initBody(e)}b.prototype.clone=function(){return new b(this,{body:this._bodyInit})},m.call(b.prototype),m.call(w.prototype),w.prototype.clone=function(){return new w(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new f(this.headers),url:this.url})},w.error=function(){var e=new w(null,{status:0,statusText:""});return e.type="error",e};var T=[301,302,303,307,308];w.redirect=function(e,t){if(-1===T.indexOf(t))throw new RangeError("Invalid status code");return new w(null,{status:t,headers:{location:e}})},t.DOMException=e.DOMException;try{new t.DOMException}catch(e){t.DOMException=function(e,t){this.message=e,this.name=t;var r=Error(e);this.stack=r.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}function P(e,r){return new Promise((function(n,o){var a=new b(e,r);if(a.signal&&a.signal.aborted)return o(new t.DOMException("Aborted","AbortError"));var s=new XMLHttpRequest;function u(){s.abort()}s.onload=function(){var e,t,r={status:s.status,statusText:s.statusText,headers:(e=s.getAllResponseHeaders()||"",t=new f,e.replace(/\r?\n[\t ]+/g," ").split(/\r?\n/).forEach((function(e){var r=e.split(":"),n=r.shift().trim();if(n){var i=r.join(":").trim();t.append(n,i)}})),t)};r.url="responseURL"in s?s.responseURL:r.headers.get("X-Request-URL");var i="response"in s?s.response:s.responseText;n(new w(i,r))},s.onerror=function(){o(new TypeError("Network request failed"))},s.ontimeout=function(){o(new TypeError("Network request failed"))},s.onabort=function(){o(new t.DOMException("Aborted","AbortError"))},s.open(a.method,a.url,!0),"include"===a.credentials?s.withCredentials=!0:"omit"===a.credentials&&(s.withCredentials=!1),"responseType"in s&&i&&(s.responseType="blob"),a.headers.forEach((function(e,t){s.setRequestHeader(t,e)})),a.signal&&(a.signal.addEventListener("abort",u),s.onreadystatechange=function(){4===s.readyState&&a.signal.removeEventListener("abort",u)}),s.send(void 0===a._bodyInit?null:a._bodyInit)}))}P.polyfill=!0,e.fetch||(e.fetch=P,e.Headers=f,e.Request=b,e.Response=w),t.Headers=f,t.Request=b,t.Response=w,t.fetch=P,Object.defineProperty(t,"__esModule",{value:!0})}({})}(n),n.fetch.ponyfill=!0,delete n.fetch.polyfill;var i=n;(t=i.fetch).default=i.fetch,t.fetch=i.fetch,t.Headers=i.Headers,t.Request=i.Request,t.Response=i.Response,e.exports=t},622:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.LazPerf=t.create=t.createLazPerf=void 0;const i=n(r(377));t.createLazPerf=i.default,t.create=i.default,t.LazPerf={create:i.default}},377:e=>{var t,r=(t="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(e){var r,n,i=void 0!==(e=e||{})?e:{};i.ready=new Promise((function(e,t){r=e,n=t})),["_main","___getTypeName","__embind_initialize_bindings","_fflush","onRuntimeInitialized"].forEach((e=>{Object.getOwnPropertyDescriptor(i.ready,e)||Object.defineProperty(i.ready,e,{get:()=>z("You are getting "+e+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js"),set:()=>z("You are setting "+e+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")})}));var o=Object.assign({},i),a=[],s="./this.program";if(i.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)");var u,c="";if("undefined"!=typeof document&&document.currentScript&&(c=document.currentScript.src),t&&(c=t),c=0!==c.indexOf("blob:")?c.substr(0,c.replace(/[?#].*/,"").lastIndexOf("/")+1):"","object"!=typeof window&&"function"!=typeof importScripts)throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");var l,d,f,p=i.print||console.log.bind(console),h=i.printErr||console.warn.bind(console);function g(e,t){Object.getOwnPropertyDescriptor(i,e)||Object.defineProperty(i,e,{configurable:!0,get:function(){z("Module."+e+" has been replaced with plain "+t+" (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}})}function y(e){return"FS_createPath"===e||"FS_createDataFile"===e||"FS_createPreloadedFile"===e||"FS_unlink"===e||"addRunDependency"===e||"FS_createLazyFile"===e||"FS_createDevice"===e||"removeRunDependency"===e}Object.assign(i,o),o=null,l="fetchSettings",Object.getOwnPropertyDescriptor(i,l)&&z("`Module."+l+"` was supplied but `"+l+"` not included in INCOMING_MODULE_JS_API"),i.arguments&&(a=i.arguments),g("arguments","arguments_"),i.thisProgram&&(s=i.thisProgram),g("thisProgram","thisProgram"),i.quit&&i.quit,g("quit","quit_"),v(void 0===i.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),v(void 0===i.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),v(void 0===i.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),v(void 0===i.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),v(void 0===i.read,"Module.read option was removed (modify read_ in JS)"),v(void 0===i.readAsync,"Module.readAsync option was removed (modify readAsync in JS)"),v(void 0===i.readBinary,"Module.readBinary option was removed (modify readBinary in JS)"),v(void 0===i.setWindowTitle,"Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),v(void 0===i.TOTAL_MEMORY,"Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),g("read","read_"),g("readAsync","readAsync"),g("readBinary","readBinary"),g("setWindowTitle","setWindowTitle"),v(!0,"worker environment detected but not enabled at build time.  Add 'worker' to `-sENVIRONMENT` to enable."),v(!0,"node environment detected but not enabled at build time.  Add 'node' to `-sENVIRONMENT` to enable."),v(!0,"shell environment detected but not enabled at build time.  Add 'shell' to `-sENVIRONMENT` to enable."),i.wasmBinary&&(d=i.wasmBinary),g("wasmBinary","wasmBinary"),i.noExitRuntime,g("noExitRuntime","noExitRuntime"),"object"!=typeof WebAssembly&&z("no native wasm support detected");var m=!1;function v(e,t){e||z("Assertion failed"+(t?": "+t:""))}var b,_,w,T,P,E,C,O,S,I="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function F(e,t,r){for(var n=t+r,i=t;e[i]&&!(i>=n);)++i;if(i-t>16&&e.buffer&&I)return I.decode(e.subarray(t,i));for(var o="";t<i;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var u=63&e[t++];if(224==(240&a)?a=(15&a)<<12|s<<6|u:(240!=(248&a)&&ee("Invalid UTF-8 leading byte 0x"+a.toString(16)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),a=(7&a)<<18|s<<12|u<<6|63&e[t++]),a<65536)o+=String.fromCharCode(a);else{var c=a-65536;o+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else o+=String.fromCharCode((31&a)<<6|s)}else o+=String.fromCharCode(a)}return o}function D(e,t){return e?F(w,e,t):""}function A(e,t,r,n){if(!(n>0))return 0;for(var i=r,o=r+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(r>=o)break;t[r++]=s}else if(s<=2047){if(r+1>=o)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=o)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=o)break;s>1114111&&ee("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF)."),t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-i}function L(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t}function M(e){b=e,i.HEAP8=_=new Int8Array(e),i.HEAP16=T=new Int16Array(e),i.HEAP32=E=new Int32Array(e),i.HEAPU8=w=new Uint8Array(e),i.HEAPU16=P=new Uint16Array(e),i.HEAPU32=C=new Uint32Array(e),i.HEAPF32=O=new Float32Array(e),i.HEAPF64=S=new Float64Array(e)}var U=65536;i.TOTAL_STACK&&v(U===i.TOTAL_STACK,"the stack size can no longer be determined at runtime");var R,j=i.INITIAL_MEMORY||262144;function k(){if(!m){var e=Et(),t=C[e>>2],r=C[e+4>>2];34821223==t&&2310721022==r||z("Stack overflow! Stack cookie has been overwritten at 0x"+e.toString(16)+", expected hex dwords 0x89BACDFE and 0x2135467, but received 0x"+r.toString(16)+" 0x"+t.toString(16)),1668509029!==C[0]&&z("Runtime error: The application has corrupted its heap memory area (address zero)!")}}g("INITIAL_MEMORY","INITIAL_MEMORY"),v(j>=U,"INITIAL_MEMORY should be larger than TOTAL_STACK, was "+j+"! (TOTAL_STACK="+U+")"),v("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&null!=Int32Array.prototype.subarray&&null!=Int32Array.prototype.set,"JS engine does not provide full typed array support"),v(!i.wasmMemory,"Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally"),v(262144==j,"Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically"),function(){var e=new Int16Array(1),t=new Int8Array(e.buffer);if(e[0]=25459,115!==t[0]||99!==t[1])throw"Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"}();var x=[],B=[],$=[],N=!1;v(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),v(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),v(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),v(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var W=0,H=null,V=null,G={};function z(e){i.onAbort&&i.onAbort(e),h(e="Aborted("+e+")"),m=!0;var t=new WebAssembly.RuntimeError(e);throw n(t),t}var Y={error:function(){z("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with -sFORCE_FILESYSTEM")},init:function(){Y.error()},createDataFile:function(){Y.error()},createPreloadedFile:function(){Y.error()},createLazyFile:function(){Y.error()},open:function(){Y.error()},mkdev:function(){Y.error()},registerDevice:function(){Y.error()},analyzePath:function(){Y.error()},loadFilesFromDB:function(){Y.error()},ErrnoError:function(){Y.error()}};i.FS_createDataFile=Y.createDataFile,i.FS_createPreloadedFile=Y.createPreloadedFile;var q,J;function K(e){return e.startsWith("data:application/octet-stream;base64,")}function X(e,t){return function(){var r=e,n=t;return t||(n=i.asm),v(N,"native function `"+r+"` called before runtime initialization"),n[e]||v(n[e],"exported native function `"+r+"` not found"),n[e].apply(null,arguments)}}function Z(e){try{if(e==q&&d)return new Uint8Array(d);if(u)return u(e);throw"both async and sync fetching of the wasm failed"}catch(e){z(e)}}function Q(e){for(;e.length>0;)e.shift()(i)}function ee(e){ee.shown||(ee.shown={}),ee.shown[e]||(ee.shown[e]=1,h(e))}function te(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){C[this.ptr+4>>2]=e},this.get_type=function(){return C[this.ptr+4>>2]},this.set_destructor=function(e){C[this.ptr+8>>2]=e},this.get_destructor=function(){return C[this.ptr+8>>2]},this.set_refcount=function(e){E[this.ptr>>2]=e},this.set_caught=function(e){e=e?1:0,_[this.ptr+12>>0]=e},this.get_caught=function(){return 0!=_[this.ptr+12>>0]},this.set_rethrown=function(e){e=e?1:0,_[this.ptr+13>>0]=e},this.get_rethrown=function(){return 0!=_[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=E[this.ptr>>2];E[this.ptr>>2]=e+1},this.release_ref=function(){var e=E[this.ptr>>2];return E[this.ptr>>2]=e-1,v(e>0),1===e},this.set_adjusted_ptr=function(e){C[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return C[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Ct(this.get_type()))return C[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}function re(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}K(q="laz-perf.wasm")||(J=q,q=i.locateFile?i.locateFile(J,c):c+J);var ne=void 0;function ie(e){for(var t="",r=e;w[r];)t+=ne[w[r++]];return t}var oe={},ae={},se={},ue=48,ce=57;function le(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=ue&&t<=ce?"_"+e:e}function de(e,t){return e=le(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function fe(e,t){var r=de(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var pe=void 0;function he(e){throw new pe(e)}var ge=void 0;function ye(e){throw new ge(e)}function me(e,t,r){function n(t){var n=r(t);n.length!==e.length&&ye("Mismatched type converter count");for(var i=0;i<e.length;++i)ve(e[i],n[i])}e.forEach((function(e){se[e]=t}));var i=new Array(t.length),o=[],a=0;t.forEach(((e,t)=>{ae.hasOwnProperty(e)?i[t]=ae[e]:(o.push(e),oe.hasOwnProperty(e)||(oe[e]=[]),oe[e].push((()=>{i[t]=ae[e],++a===o.length&&n(i)})))})),0===o.length&&n(i)}function ve(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||he('type "'+n+'" must have a positive integer typeid pointer'),ae.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;he("Cannot register type '"+n+"' twice")}if(ae[e]=t,delete se[e],oe.hasOwnProperty(e)){var i=oe[e];delete oe[e],i.forEach((e=>e()))}}function be(e){he(e.$$.ptrType.registeredClass.name+" instance already deleted")}var _e=!1;function we(e){}function Te(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Pe(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=Pe(e,t,r.baseClass);return null===n?null:r.downcast(n)}var Ee={};var Ce=[];function Oe(){for(;Ce.length;){var e=Ce.pop();e.$$.deleteScheduled=!1,e.delete()}}var Se=void 0;var Ie={};function Fe(e,t){return t.ptrType&&t.ptr||ye("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&ye("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Ae(Object.create(e,{$$:{value:t}}))}function De(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=function(e,t){return t=function(e,t){for(void 0===t&&he("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),Ie[t]}(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function i(){return this.isSmartPointer?Fe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Fe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var o,a=this.registeredClass.getActualType(t),s=Ee[a];if(!s)return i.call(this);o=this.isConst?s.constPointerType:s.pointerType;var u=Pe(t,this.registeredClass,o.registeredClass);return null===u?i.call(this):this.isSmartPointer?Fe(o.registeredClass.instancePrototype,{ptrType:o,ptr:u,smartPtrType:this,smartPtr:e}):Fe(o.registeredClass.instancePrototype,{ptrType:o,ptr:u})}function Ae(e){return"undefined"==typeof FinalizationRegistry?(Ae=e=>e,e):(_e=new FinalizationRegistry((e=>{console.warn(e.leakWarning.stack.replace(/^Error: /,"")),Te(e.$$)})),Ae=e=>{var t=e.$$;if(t.smartPtr){var r={$$:t},n=t.ptrType.registeredClass;r.leakWarning=new Error("Embind found a leaked C++ instance "+n.name+" <0x"+t.ptr.toString(16)+">.\nWe'll free it automatically in this case, but this functionality is not reliable across various environments.\nMake sure to invoke .delete() manually once you're done with the instance instead.\nOriginally allocated"),"captureStackTrace"in Error&&Error.captureStackTrace(r.leakWarning,De),_e.register(e,r,e)}return e},we=e=>_e.unregister(e),Ae(e))}function Le(){}function Me(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||he("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function Ue(e,t,r,n,i,o,a,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=i,this.getActualType=o,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function Re(e,t,r){for(;t!==r;)t.upcast||he("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function je(e,t){if(null===t)return this.isReference&&he("null is not a valid "+this.name),0;t.$$||he('Cannot pass "'+Qe(t)+'" as a '+this.name),t.$$.ptr||he("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Re(t.$$.ptr,r,this.registeredClass)}function ke(e,t){var r;if(null===t)return this.isReference&&he("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||he('Cannot pass "'+Qe(t)+'" as a '+this.name),t.$$.ptr||he("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&he("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;if(r=Re(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&he("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:he("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var i=t.clone();r=this.rawShare(r,Ze.toHandle((function(){i.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:he("Unsupporting sharing policy")}return r}function xe(e,t){if(null===t)return this.isReference&&he("null is not a valid "+this.name),0;t.$$||he('Cannot pass "'+Qe(t)+'" as a '+this.name),t.$$.ptr||he("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&he("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Re(t.$$.ptr,r,this.registeredClass)}function Be(e){return this.fromWireType(E[e>>2])}function $e(e,t,r,n,i,o,a,s,u,c,l){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=i,this.pointeeType=o,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,i||void 0!==t.baseClass?this.toWireType=ke:n?(this.toWireType=je,this.destructorFunction=null):(this.toWireType=xe,this.destructorFunction=null)}var Ne=[];function We(e){var t=Ne[e];return t||(e>=Ne.length&&(Ne.length=e+1),Ne[e]=t=R.get(e)),v(R.get(e)==t,"JavaScript-side Wasm function table mirror is out of date!"),t}function He(e,t){var r=(e=ie(e)).includes("j")?function(e,t){v(e.includes("j")||e.includes("p"),"getDynCaller should only be called with i64 sigs");var r=[];return function(){return r.length=0,Object.assign(r,arguments),function(e,t,r){return e.includes("j")?function(e,t,r){v("dynCall_"+e in i,"bad function pointer type - no table for sig '"+e+"'"),r&&r.length?v(r.length===e.substring(1).replace(/j/g,"--").length):v(1==e.length);var n=i["dynCall_"+e];return r&&r.length?n.apply(null,[t].concat(r)):n.call(null,t)}(e,t,r):(v(We(t),"missing table entry in dynCall: "+t),We(t).apply(null,r))}(e,t,r)}}(e,t):We(t);return"function"!=typeof r&&he("unknown function pointer with signature "+e+": "+t),r}var Ve=void 0;function Ge(e){var t=Tt(e),r=ie(t);return wt(t),r}function ze(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||ae[t]||(se[t]?se[t].forEach(e):(r.push(t),n[t]=!0))})),new Ve(e+": "+r.map(Ge).join([", "]))}function Ye(e,t){for(var r=[],n=0;n<e;n++)r.push(C[t+4*n>>2]);return r}function qe(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function Je(e,t,r,n,i){var o=t.length;o<2&&he("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==t[1]&&null!==r,s=!1,u=1;u<t.length;++u)if(null!==t[u]&&void 0===t[u].destructorFunction){s=!0;break}var c="void"!==t[0].name,l="",d="";for(u=0;u<o-2;++u)l+=(0!==u?", ":"")+"arg"+u,d+=(0!==u?", ":"")+"arg"+u+"Wired";var f="return function "+le(e)+"("+l+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";s&&(f+="var destructors = [];\n");var p=s?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],g=[he,n,i,qe,t[0],t[1]];for(a&&(f+="var thisWired = classParam.toWireType("+p+", this);\n"),u=0;u<o-2;++u)f+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+t[u+2].name+"\n",h.push("argType"+u),g.push(t[u+2]);if(a&&(d="thisWired"+(d.length>0?", ":"")+d),f+=(c?"var rv = ":"")+"invoker(fn"+(d.length>0?", ":"")+d+");\n",s)f+="runDestructors(destructors);\n";else for(u=a?1:2;u<t.length;++u){var y=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==t[u].destructorFunction&&(f+=y+"_dtor("+y+"); // "+t[u].name+"\n",h.push(y+"_dtor"),g.push(t[u].destructorFunction))}return c&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",h.push(f),function(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=de(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,i=e.apply(n,t);return i instanceof Object?i:n}(Function,h).apply(null,g)}var Ke=[],Xe=[{},{value:void 0},{value:null},{value:!0},{value:!1}];var Ze={toValue:e=>(e||he("Cannot use deleted val. handle = "+e),Xe[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Ke.length?Ke.pop():Xe.length;return Xe[t]={refcount:1,value:e},t}}};function Qe(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function et(e,t){switch(t){case 2:return function(e){return this.fromWireType(O[e>>2])};case 3:return function(e){return this.fromWireType(S[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function tt(e,t,r){switch(t){case 0:return r?function(e){return _[e]}:function(e){return w[e]};case 1:return r?function(e){return T[e>>1]}:function(e){return P[e>>1]};case 2:return r?function(e){return E[e>>2]}:function(e){return C[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var rt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function nt(e,t){v(e%2==0,"Pointer passed to UTF16ToString must be aligned to two bytes!");for(var r=e,n=r>>1,i=n+t/2;!(n>=i)&&P[n];)++n;if((r=n<<1)-e>32&&rt)return rt.decode(w.subarray(e,r));for(var o="",a=0;!(a>=t/2);++a){var s=T[e+2*a>>1];if(0==s)break;o+=String.fromCharCode(s)}return o}function it(e,t,r){if(v(t%2==0,"Pointer passed to stringToUTF16 must be aligned to two bytes!"),v("number"==typeof r,"stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,i=(r-=2)<2*e.length?r/2:e.length,o=0;o<i;++o){var a=e.charCodeAt(o);T[t>>1]=a,t+=2}return T[t>>1]=0,t-n}function ot(e){return 2*e.length}function at(e,t){v(e%4==0,"Pointer passed to UTF32ToString must be aligned to four bytes!");for(var r=0,n="";!(r>=t/4);){var i=E[e+4*r>>2];if(0==i)break;if(++r,i>=65536){var o=i-65536;n+=String.fromCharCode(55296|o>>10,56320|1023&o)}else n+=String.fromCharCode(i)}return n}function st(e,t,r){if(v(t%4==0,"Pointer passed to stringToUTF32 must be aligned to four bytes!"),v("number"==typeof r,"stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,i=n+r-4,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),E[t>>2]=a,(t+=4)+4>i)break}return E[t>>2]=0,t-n}function ut(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t}function ct(e){try{return f.grow(e-b.byteLength+65535>>>16),M(f.buffer),1}catch(t){h("emscripten_realloc_buffer: Attempted to grow heap from "+b.byteLength+" bytes to "+e+" bytes, but got error: "+t)}}var lt={};function dt(){if(!dt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:s||"./this.program"};for(var t in lt)void 0===lt[t]?delete e[t]:e[t]=lt[t];var r=[];for(var t in e)r.push(t+"="+e[t]);dt.strings=r}return dt.strings}var ft=[null,[],[]];function pt(e,t){var r=ft[e];v(r),0===t||10===t?((1===e?p:h)(F(r,0)),r.length=0):r.push(t)}function ht(e){return e%4==0&&(e%100!=0||e%400==0)}var gt=[31,29,31,30,31,30,31,31,30,31,30,31],yt=[31,28,31,30,31,30,31,31,30,31,30,31];function mt(e,t,r,n){var i=E[n+40>>2],o={tm_sec:E[n>>2],tm_min:E[n+4>>2],tm_hour:E[n+8>>2],tm_mday:E[n+12>>2],tm_mon:E[n+16>>2],tm_year:E[n+20>>2],tm_wday:E[n+24>>2],tm_yday:E[n+28>>2],tm_isdst:E[n+32>>2],tm_gmtoff:E[n+36>>2],tm_zone:i?D(i):""},a=D(r),s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var u in s)a=a.replace(new RegExp(u,"g"),s[u]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],l=["January","February","March","April","May","June","July","August","September","October","November","December"];function d(e,t,r){for(var n="number"==typeof e?e.toString():e||"";n.length<t;)n=r[0]+n;return n}function f(e,t){return d(e,t,"0")}function p(e,t){function r(e){return e<0?-1:e>0?1:0}var n;return 0===(n=r(e.getFullYear()-t.getFullYear()))&&0===(n=r(e.getMonth()-t.getMonth()))&&(n=r(e.getDate()-t.getDate())),n}function h(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function g(e){var t=function(e,t){for(var r=new Date(e.getTime());t>0;){var n=ht(r.getFullYear()),i=r.getMonth(),o=(n?gt:yt)[i];if(!(t>o-r.getDate()))return r.setDate(r.getDate()+t),r;t-=o-r.getDate()+1,r.setDate(1),i<11?r.setMonth(i+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1))}return r}(new Date(e.tm_year+1900,0,1),e.tm_yday),r=new Date(t.getFullYear(),0,4),n=new Date(t.getFullYear()+1,0,4),i=h(r),o=h(n);return p(i,t)<=0?p(o,t)<=0?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var y={"%a":function(e){return c[e.tm_wday].substring(0,3)},"%A":function(e){return c[e.tm_wday]},"%b":function(e){return l[e.tm_mon].substring(0,3)},"%B":function(e){return l[e.tm_mon]},"%C":function(e){return f((e.tm_year+1900)/100|0,2)},"%d":function(e){return f(e.tm_mday,2)},"%e":function(e){return d(e.tm_mday,2," ")},"%g":function(e){return g(e).toString().substring(2)},"%G":function(e){return g(e)},"%H":function(e){return f(e.tm_hour,2)},"%I":function(e){var t=e.tm_hour;return 0==t?t=12:t>12&&(t-=12),f(t,2)},"%j":function(e){return f(e.tm_mday+function(e,t){for(var r=0,n=0;n<=t;r+=e[n++]);return r}(ht(e.tm_year+1900)?gt:yt,e.tm_mon-1),3)},"%m":function(e){return f(e.tm_mon+1,2)},"%M":function(e){return f(e.tm_min,2)},"%n":function(){return"\n"},"%p":function(e){return e.tm_hour>=0&&e.tm_hour<12?"AM":"PM"},"%S":function(e){return f(e.tm_sec,2)},"%t":function(){return"\t"},"%u":function(e){return e.tm_wday||7},"%U":function(e){var t=e.tm_yday+7-e.tm_wday;return f(Math.floor(t/7),2)},"%V":function(e){var t=Math.floor((e.tm_yday+7-(e.tm_wday+6)%7)/7);if((e.tm_wday+371-e.tm_yday-2)%7<=2&&t++,t){if(53==t){var r=(e.tm_wday+371-e.tm_yday)%7;4==r||3==r&&ht(e.tm_year)||(t=1)}}else{t=52;var n=(e.tm_wday+7-e.tm_yday-1)%7;(4==n||5==n&&ht(e.tm_year%400-1))&&t++}return f(t,2)},"%w":function(e){return e.tm_wday},"%W":function(e){var t=e.tm_yday+7-(e.tm_wday+6)%7;return f(Math.floor(t/7),2)},"%y":function(e){return(e.tm_year+1900).toString().substring(2)},"%Y":function(e){return e.tm_year+1900},"%z":function(e){var t=e.tm_gmtoff,r=t>=0;return t=(t=Math.abs(t)/60)/60*100+t%60,(r?"+":"-")+String("0000"+t).slice(-4)},"%Z":function(e){return e.tm_zone},"%%":function(){return"%"}};for(var u in a=a.replace(/%%/g,"\0\0"),y)a.includes(u)&&(a=a.replace(new RegExp(u,"g"),y[u](o)));var m,b,w,T=(!1,b=L(m=a=a.replace(/\0\0/g,"%"))+1,A(m,w=new Array(b),0,w.length),w);return T.length>t?0:(function(e,t){v(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),_.set(e,t)}(T,e),T.length-1)}!function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);ne=e}(),pe=i.BindingError=fe(Error,"BindingError"),ge=i.InternalError=fe(Error,"InternalError"),Le.prototype.isAliasOf=function(e){if(!(this instanceof Le))return!1;if(!(e instanceof Le))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,i=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)i=n.upcast(i),n=n.baseClass;return t===n&&r===i},Le.prototype.clone=function(){if(this.$$.ptr||be(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=Ae(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t},Le.prototype.delete=function(){this.$$.ptr||be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&he("Object already scheduled for deletion"),we(this),Te(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},Le.prototype.isDeleted=function(){return!this.$$.ptr},Le.prototype.deleteLater=function(){return this.$$.ptr||be(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&he("Object already scheduled for deletion"),Ce.push(this),1===Ce.length&&Se&&Se(Oe),this.$$.deleteScheduled=!0,this},i.getInheritedInstanceCount=function(){return Object.keys(Ie).length},i.getLiveInheritedInstances=function(){var e=[];for(var t in Ie)Ie.hasOwnProperty(t)&&e.push(Ie[t]);return e},i.flushPendingDeletes=Oe,i.setDelayFunction=function(e){Se=e,Ce.length&&Se&&Se(Oe)},$e.prototype.getPointee=function(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},$e.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},$e.prototype.argPackAdvance=8,$e.prototype.readValueFromPointer=Be,$e.prototype.deleteObject=function(e){null!==e&&e.delete()},$e.prototype.fromWireType=De,Ve=i.UnboundTypeError=fe(Error,"UnboundTypeError"),i.count_emval_handles=function(){for(var e=0,t=5;t<Xe.length;++t)void 0!==Xe[t]&&++e;return e},i.get_first_emval=function(){for(var e=5;e<Xe.length;++e)if(void 0!==Xe[e])return Xe[e];return null};var vt,bt={__cxa_allocate_exception:function(e){return _t(e+24)+24},__cxa_throw:function(e,t,r){throw new te(e).init(t,r),e+" - Exception catching is disabled, this exception cannot be caught. Compile with -sNO_DISABLE_EXCEPTION_CATCHING or -sEXCEPTION_CATCHING_ALLOWED=[..] to catch."},_embind_register_bigint:function(e,t,r,n,i){},_embind_register_bool:function(e,t,r,n,i){var o=re(r);ve(e,{name:t=ie(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:i},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=_;else if(2===r)n=T;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=E}return this.fromWireType(n[e>>o])},destructorFunction:null})},_embind_register_class:function(e,t,r,n,o,a,s,u,c,l,d,f,p){d=ie(d),a=He(o,a),u&&(u=He(s,u)),l&&(l=He(c,l)),p=He(f,p);var h=le(d);!function(e,t,r){i.hasOwnProperty(e)?(he("Cannot register public name '"+e+"' twice"),Me(i,e,e),i.hasOwnProperty(r)&&he("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),i[e].overloadTable[void 0]=t):i[e]=t}(h,(function(){ze("Cannot construct "+d+" due to unbound types",[n])})),me([e,t,r],n?[n]:[],(function(t){var r,o;t=t[0],o=n?(r=t.registeredClass).instancePrototype:Le.prototype;var s=de(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new pe("Use 'new' to construct "+d);if(void 0===f.constructor_body)throw new pe(d+" has no accessible constructor");var e=f.constructor_body[arguments.length];if(void 0===e)throw new pe("Tried to invoke ctor of "+d+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(f.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),c=Object.create(o,{constructor:{value:s}});s.prototype=c;var f=new Ue(d,s,c,p,r,a,u,l),g=new $e(d,f,!0,!1,!1),y=new $e(d+"*",f,!1,!1,!1),m=new $e(d+" const*",f,!1,!0,!1);return Ee[e]={pointerType:y,constPointerType:m},function(e,t,r){i.hasOwnProperty(e)||ye("Replacing nonexistant public symbol"),i[e].overloadTable,i[e]=t,i[e].argCount=r}(h,s),[g,y,m]}))},_embind_register_class_constructor:function(e,t,r,n,i,o){v(t>0);var a=Ye(t,r);i=He(n,i),me([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new pe("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=()=>{ze("Cannot construct "+e.name+" due to unbound types",a)},me([],a,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=Je(r,n,null,i,o),[]})),[]}))},_embind_register_class_function:function(e,t,r,n,i,o,a,s){var u=Ye(r,n);t=ie(t),o=He(i,o),me([],[e],(function(e){var n=(e=e[0]).name+"."+t;function i(){ze("Cannot call "+n+" due to unbound types",u)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var c=e.registeredClass.instancePrototype,l=c[t];return void 0===l||void 0===l.overloadTable&&l.className!==e.name&&l.argCount===r-2?(i.argCount=r-2,i.className=e.name,c[t]=i):(Me(c,t,n),c[t].overloadTable[r-2]=i),me([],u,(function(i){var s=Je(n,i,e,o,a);return void 0===c[t].overloadTable?(s.argCount=r-2,c[t]=s):c[t].overloadTable[r-2]=s,[]})),[]}))},_embind_register_emval:function(e,t){ve(e,{name:t=ie(t),fromWireType:function(e){var t=Ze.toValue(e);return function(e){e>4&&0==--Xe[e].refcount&&(Xe[e]=void 0,Ke.push(e))}(e),t},toWireType:function(e,t){return Ze.toHandle(t)},argPackAdvance:8,readValueFromPointer:Be,destructorFunction:null})},_embind_register_float:function(e,t,r){var n=re(r);ve(e,{name:t=ie(t),fromWireType:function(e){return e},toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Qe(t)+'" to '+this.name);return t},argPackAdvance:8,readValueFromPointer:et(t,n),destructorFunction:null})},_embind_register_integer:function(e,t,r,n,i){t=ie(t),-1===i&&(i=4294967295);var o=re(r),a=e=>e;if(0===n){var s=32-8*r;a=e=>e<<s>>>s}var u=t.includes("unsigned"),c=(e,r)=>{if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+Qe(e)+'" to '+r);if(e<n||e>i)throw new TypeError('Passing a number "'+Qe(e)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+n+", "+i+"]!")};ve(e,{name:t,fromWireType:a,toWireType:u?function(e,t){return c(t,this.name),t>>>0}:function(e,t){return c(t,this.name),t},argPackAdvance:8,readValueFromPointer:tt(t,o,0!==n),destructorFunction:null})},_embind_register_memory_view:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function i(e){var t=C,r=t[e>>=2],i=t[e+1];return new n(b,i,r)}ve(e,{name:r=ie(r),fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{ignoreDuplicateRegistrations:!0})},_embind_register_std_string:function(e,t){var r="std::string"===(t=ie(t));ve(e,{name:t,fromWireType:function(e){var t,n=C[e>>2],i=e+4;if(r)for(var o=i,a=0;a<=n;++a){var s=i+a;if(a==n||0==w[s]){var u=D(o,s-o);void 0===t?t=u:(t+=String.fromCharCode(0),t+=u),o=s+1}}else{var c=new Array(n);for(a=0;a<n;++a)c[a]=String.fromCharCode(w[i+a]);t=c.join("")}return wt(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var i="string"==typeof t;i||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||he("Cannot pass non-string to std::string"),n=r&&i?L(t):t.length;var o,a,s,u=_t(4+n+1),c=u+4;if(C[u>>2]=n,r&&i)o=t,a=c,v("number"==typeof(s=n+1),"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),A(o,w,a,s);else if(i)for(var l=0;l<n;++l){var d=t.charCodeAt(l);d>255&&(wt(c),he("String has UTF-16 code units that do not fit in 8 bits")),w[c+l]=d}else for(l=0;l<n;++l)w[c+l]=t[l];return null!==e&&e.push(wt,u),u},argPackAdvance:8,readValueFromPointer:Be,destructorFunction:function(e){wt(e)}})},_embind_register_std_wstring:function(e,t,r){var n,i,o,a,s;r=ie(r),2===t?(n=nt,i=it,a=ot,o=()=>P,s=1):4===t&&(n=at,i=st,a=ut,o=()=>C,s=2),ve(e,{name:r,fromWireType:function(e){for(var r,i=C[e>>2],a=o(),u=e+4,c=0;c<=i;++c){var l=e+4+c*t;if(c==i||0==a[l>>s]){var d=n(u,l-u);void 0===r?r=d:(r+=String.fromCharCode(0),r+=d),u=l+t}}return wt(e),r},toWireType:function(e,n){"string"!=typeof n&&he("Cannot pass non-string to C++ string type "+r);var o=a(n),u=_t(4+o+t);return C[u>>2]=o>>s,i(n,u+4,o+t),null!==e&&e.push(wt,u),u},argPackAdvance:8,readValueFromPointer:Be,destructorFunction:function(e){wt(e)}})},_embind_register_void:function(e,t){ve(e,{isVoid:!0,name:t=ie(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},abort:function(){z("native code called abort()")},emscripten_memcpy_big:function(e,t,r){w.copyWithin(e,t,t+r)},emscripten_resize_heap:function(e){var t=w.length;v((e>>>=0)>t);var r,n=2147483648;if(e>n)return h("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+n+" bytes!"),!1;for(var i=1;i<=4;i*=2){var o=t*(1+.2/i);o=Math.min(o,e+100663296);var a=Math.min(n,(r=Math.max(e,o))+(65536-r%65536)%65536);if(ct(a))return!0}return h("Failed to grow the heap from "+t+" bytes to "+a+" bytes, not enough memory!"),!1},environ_get:function(e,t){var r=0;return dt().forEach((function(n,i){var o=t+r;C[e+4*i>>2]=o,function(e,t,r){for(var n=0;n<e.length;++n)v(e.charCodeAt(n)===(255&e.charCodeAt(n))),_[t++>>0]=e.charCodeAt(n);_[t>>0]=0}(n,o),r+=n.length+1})),0},environ_sizes_get:function(e,t){var r=dt();C[e>>2]=r.length;var n=0;return r.forEach((function(e){n+=e.length+1})),C[t>>2]=n,0},fd_close:function(e){z("fd_close called without SYSCALLS_REQUIRE_FILESYSTEM")},fd_seek:function(e,t,r,n,i){return 70},fd_write:function(e,t,r,n){for(var i=0,o=0;o<r;o++){var a=C[t>>2],s=C[t+4>>2];t+=8;for(var u=0;u<s;u++)pt(e,w[a+u]);i+=s}return C[n>>2]=i,0},strftime_l:function(e,t,r,n){return mt(e,t,r,n)}},_t=(function(){var e,t={env:bt,wasi_snapshot_preview1:bt};function r(e,t){var r,n=e.exports;i.asm=n,v(f=i.asm.memory,"memory not found in wasm exports"),M(f.buffer),v(R=i.asm.__indirect_function_table,"table not found in wasm exports"),r=i.asm.__wasm_call_ctors,B.unshift(r),function(e){if(W--,i.monitorRunDependencies&&i.monitorRunDependencies(W),e?(v(G[e]),delete G[e]):h("warning: run dependency removed without ID"),0==W&&(null!==H&&(clearInterval(H),H=null),V)){var t=V;V=null,t()}}("wasm-instantiate")}e="wasm-instantiate",W++,i.monitorRunDependencies&&i.monitorRunDependencies(W),e?(v(!G[e]),G[e]=1,null===H&&"undefined"!=typeof setInterval&&(H=setInterval((function(){if(m)return clearInterval(H),void(H=null);var e=!1;for(var t in G)e||(e=!0,h("still waiting on run dependencies:")),h("dependency: "+t);e&&h("(end of list)")}),1e4))):h("warning: run dependency added without ID");var o=i;function a(e){v(i===o,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),o=null,r(e.instance)}function s(e){return(d||"function"!=typeof fetch?Promise.resolve().then((function(){return Z(q)})):fetch(q,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+q+"'";return e.arrayBuffer()})).catch((function(){return Z(q)}))).then((function(e){return WebAssembly.instantiate(e,t)})).then((function(e){return e})).then(e,(function(e){h("failed to asynchronously prepare wasm: "+e),q.startsWith("file://")&&h("warning: Loading from a file URI ("+q+") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing"),z(e)}))}if(i.instantiateWasm)try{return i.instantiateWasm(t,r)}catch(e){return h("Module.instantiateWasm callback failed with error: "+e),!1}(d||"function"!=typeof WebAssembly.instantiateStreaming||K(q)||"function"!=typeof fetch?s(a):fetch(q,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(a,(function(e){return h("wasm streaming compile failed: "+e),h("falling back to ArrayBuffer instantiation"),s(a)}))}))).catch(n)}(),i.___wasm_call_ctors=X("__wasm_call_ctors"),i._malloc=X("malloc")),wt=i._free=X("free"),Tt=i.___getTypeName=X("__getTypeName"),Pt=(i.__embind_initialize_bindings=X("_embind_initialize_bindings"),i.___errno_location=X("__errno_location"),i._fflush=X("fflush"),i._emscripten_stack_init=function(){return(Pt=i._emscripten_stack_init=i.asm.emscripten_stack_init).apply(null,arguments)}),Et=(i._emscripten_stack_get_free=function(){return(i._emscripten_stack_get_free=i.asm.emscripten_stack_get_free).apply(null,arguments)},i._emscripten_stack_get_base=function(){return(i._emscripten_stack_get_base=i.asm.emscripten_stack_get_base).apply(null,arguments)},i._emscripten_stack_get_end=function(){return(Et=i._emscripten_stack_get_end=i.asm.emscripten_stack_get_end).apply(null,arguments)}),Ct=(i.stackSave=X("stackSave"),i.stackRestore=X("stackRestore"),i.stackAlloc=X("stackAlloc"),i.___cxa_is_pointer_type=X("__cxa_is_pointer_type"));function Ot(e){function t(){vt||(vt=!0,i.calledRun=!0,m||(v(!N),N=!0,k(),Q(B),r(i),i.onRuntimeInitialized&&i.onRuntimeInitialized(),v(!i._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),function(){if(k(),i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)e=i.postRun.shift(),$.unshift(e);var e;Q($)}()))}var n;e=e||a,W>0||(Pt(),v(0==(3&(n=Et()))),C[n>>2]=34821223,C[n+4>>2]=2310721022,C[0]=1668509029,function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)e=i.preRun.shift(),x.unshift(e);var e;Q(x)}(),W>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),t()}),1)):t(),k()))}if(i.dynCall_viijii=X("dynCall_viijii"),i.dynCall_ji=X("dynCall_ji"),i.dynCall_jiji=X("dynCall_jiji"),i.dynCall_iiiiij=X("dynCall_iiiiij"),i.dynCall_iiiiijj=X("dynCall_iiiiijj"),i.dynCall_iiiiiijj=X("dynCall_iiiiiijj"),["run","UTF8ArrayToString","UTF8ToString","stringToUTF8Array","stringToUTF8","lengthBytesUTF8","addOnPreRun","addOnInit","addOnPreMain","addOnExit","addOnPostRun","addRunDependency","removeRunDependency","FS_createFolder","FS_createPath","FS_createDataFile","FS_createPreloadedFile","FS_createLazyFile","FS_createLink","FS_createDevice","FS_unlink","getLEB","getFunctionTables","alignFunctionTables","registerFunctions","prettyPrint","getCompilerSetting","print","printErr","callMain","abort","keepRuntimeAlive","wasmMemory","stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0","writeStackCookie","checkStackCookie","ptrToString","zeroMemory","stringToNewUTF8","exitJS","getHeapMax","emscripten_realloc_buffer","ENV","ERRNO_CODES","ERRNO_MESSAGES","setErrNo","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","DNS","getHostByName","Protocols","Sockets","getRandomDevice","warnOnce","traverseStack","UNWIND_CACHE","convertPCtoSourceLocation","readAsmConstArgsArray","readAsmConstArgs","mainThreadEM_ASM","jstoi_q","jstoi_s","getExecutableName","listenOnce","autoResumeAudioContext","dynCallLegacy","getDynCaller","dynCall","handleException","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","safeSetTimeout","asmjsMangle","asyncLoad","alignMemory","mmapAlloc","writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromI64","readI53FromU64","convertI32PairToI53","convertI32PairToI53Checked","convertU32PairToI53","getCFunc","ccall","cwrap","uleb128Encode","sigToWasmTypes","convertJsFunctionToWasm","freeTableIndexes","functionsInTableMap","getEmptyTableSlot","updateTableMap","addFunction","removeFunction","reallyNegative","unSign","strLen","reSign","formatString","setValue","getValue","PATH","PATH_FS","intArrayFromString","intArrayToString","AsciiToString","stringToAscii","UTF16Decoder","UTF16ToString","stringToUTF16","lengthBytesUTF16","UTF32ToString","stringToUTF32","lengthBytesUTF32","allocateUTF8","allocateUTF8OnStack","writeStringToMemory","writeArrayToMemory","writeAsciiToMemory","SYSCALLS","getSocketFromFD","getSocketAddress","JSEvents","registerKeyEventCallback","specialHTMLTargets","maybeCStringToJsString","findEventTarget","findCanvasEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","currentFullscreenStrategy","restoreOldWindowedStyle","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","demangle","demangleAll","jsStackTrace","stackTrace","ExitStatus","getEnvStrings","checkWasiClock","flush_NO_FILESYSTEM","dlopenMissingError","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","uncaughtExceptionCount","exceptionLast","exceptionCaught","ExceptionInfo","exception_addRef","exception_decRef","Browser","setMainLoop","wget","FS","MEMFS","TTY","PIPEFS","SOCKFS","_setNetworkCallback","tempFixedLengthArray","miniTempWebGLFloatBuffers","heapObjectForWebGLType","heapAccessShiftForWebGLHeap","GL","emscriptenWebGLGet","computeUnpackAlignedImageSize","emscriptenWebGLGetTexPixelData","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","writeGLArray","AL","SDL_unicode","SDL_ttfContext","SDL_audio","SDL","SDL_gfx","GLUT","EGL","GLFW_Window","GLFW","GLEW","IDBStore","runAndAbortIfError","ALLOC_NORMAL","ALLOC_STACK","allocate","InternalError","BindingError","UnboundTypeError","PureVirtualError","init_embind","throwInternalError","throwBindingError","throwUnboundTypeError","ensureOverloadTable","exposePublicSymbol","replacePublicSymbol","extendError","createNamedFunction","embindRepr","registeredInstances","getBasestPointer","registerInheritedInstance","unregisterInheritedInstance","getInheritedInstance","getInheritedInstanceCount","getLiveInheritedInstances","registeredTypes","awaitingDependencies","typeDependencies","registeredPointers","registerType","whenDependentTypesAreResolved","embind_charCodes","embind_init_charCodes","readLatin1String","getTypeName","heap32VectorToArray","requireRegisteredType","getShiftFromSize","integerReadValueFromPointer","enumReadValueFromPointer","floatReadValueFromPointer","simpleReadValueFromPointer","runDestructors","new_","craftInvokerFunction","embind__requireFunction","tupleRegistrations","structRegistrations","genericPointerToWireType","constNoSmartPtrRawPointerToWireType","nonConstNoSmartPtrRawPointerToWireType","init_RegisteredPointer","RegisteredPointer","RegisteredPointer_getPointee","RegisteredPointer_destructor","RegisteredPointer_deleteObject","RegisteredPointer_fromWireType","runDestructor","releaseClassHandle","finalizationRegistry","detachFinalizer_deps","detachFinalizer","attachFinalizer","makeClassHandle","init_ClassHandle","ClassHandle","ClassHandle_isAliasOf","throwInstanceAlreadyDeleted","ClassHandle_clone","ClassHandle_delete","deletionQueue","ClassHandle_isDeleted","ClassHandle_deleteLater","flushPendingDeletes","delayFunction","setDelayFunction","RegisteredClass","shallowCopyInternalPointer","downcastPointer","upcastPointer","validateThis","char_0","char_9","makeLegalFunctionName","emval_handle_array","emval_free_list","emval_symbols","init_emval","count_emval_handles","get_first_emval","getStringOrSymbol","Emval","emval_newers","craftEmvalAllocator","emval_get_global","emval_lookupTypes","emval_allocateDestructors","emval_methodCallers","emval_addMethodCaller","emval_registeredMethods"].forEach((function(e){Object.getOwnPropertyDescriptor(i,e)||Object.defineProperty(i,e,{configurable:!0,get:function(){var t="'"+e+"' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)";y(e)&&(t+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),z(t)}})})),["ptrToString","zeroMemory","stringToNewUTF8","exitJS","setErrNo","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","getHostByName","getRandomDevice","traverseStack","convertPCtoSourceLocation","readAsmConstArgs","mainThreadEM_ASM","jstoi_q","jstoi_s","listenOnce","autoResumeAudioContext","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","safeSetTimeout","asmjsMangle","asyncLoad","alignMemory","mmapAlloc","writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromI64","readI53FromU64","convertI32PairToI53","convertU32PairToI53","reallyNegative","unSign","strLen","reSign","formatString","getSocketFromFD","getSocketAddress","registerKeyEventCallback","maybeCStringToJsString","findEventTarget","findCanvasEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","checkWasiClock","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","exception_addRef","exception_decRef","setMainLoop","_setNetworkCallback","heapObjectForWebGLType","heapAccessShiftForWebGLHeap","emscriptenWebGLGet","computeUnpackAlignedImageSize","emscriptenWebGLGetTexPixelData","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","writeGLArray","SDL_unicode","SDL_ttfContext","SDL_audio","GLFW_Window","runAndAbortIfError","registerInheritedInstance","unregisterInheritedInstance","requireRegisteredType","enumReadValueFromPointer","validateThis","getStringOrSymbol","craftEmvalAllocator","emval_get_global","emval_lookupTypes","emval_allocateDestructors","emval_addMethodCaller"].forEach((function(e){"undefined"==typeof globalThis||Object.getOwnPropertyDescriptor(globalThis,e)||Object.defineProperty(globalThis,e,{configurable:!0,get:function(){var t="`"+e+"` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line";y(e)&&(t+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"),ee(t)}})})),V=function e(){vt||Ot(),vt||(V=e)},i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return Ot(),e.ready});e.exports=r},963:()=>{}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n);var e=r(830),t={};for(const r in e)"default"!==r&&(t[r]=()=>e[r]);r.d(n,t)})(),Copc=n})();